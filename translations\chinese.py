# -*- coding: utf-8 -*-
"""
HardOps 中文翻译字典
Chinese Translation Dictionary for HardOps

这个文件包含了HardOps插件的所有中文翻译
This file contains all Chinese translations for the HardOps addon
"""

# 主要操作和工具翻译
OPERATIONS = {
    # Boolean Operations - 布尔运算
    "Difference": "差集",
    "Union": "并集",
    "Intersect": "交集",
    "Intersection": "交集",
    "Slash": "切割",
    "Inset": "内嵌",
    "Outset": "外嵌",
    "Knife": "刀切",
    "Knife Boolean": "刀切布尔",
    "Knife Project": "刀切投影",

    # Sharpening Operations - 锐化操作
    "Sharpen": "锐化",
    "Soft Sharpen": "软锐化",
    "Complex Sharpen": "复杂锐化",
    "Clear Sharps": "清除锐边",
    "Step": "阶梯",
    "Cstep": "C阶梯",
    "Sstep": "S阶梯",
    "Tstep": "T阶梯",

    # Modifiers - 修改器
    "Bevel": "倒角",
    "Array": "阵列",
    "Mirror": "镜像",
    "Solidify": "实体化",
    "Subdivision": "细分",
    "Wireframe": "线框",
    "Triangulate": "三角化",
    "Decimate": "精简",
    "Displace": "置换",
    "Smooth": "平滑",
    "Cast": "投射",
    "Lattice": "晶格",
    "Shrinkwrap": "收缩包裹",
    "Screw": "螺旋",
    "Weld": "焊接",
    "Skin": "蒙皮",
    "Cloth": "布料",
    "Hook": "钩子",
    "Mask": "遮罩",
    "Curve": "曲线",

    # Tools - 工具
    "Adjust": "调整",
    "Scroll": "滚动",
    "Toggle": "切换",
    "Reset": "重置",
    "Apply": "应用",
    "Remove": "移除",
    "Add": "添加",
    "Delete": "删除",
    "Copy": "复制",
    "Paste": "粘贴",
    "Duplicate": "复制",
    "Select": "选择",
    "Deselect": "取消选择",
    "Hide": "隐藏",
    "Show": "显示",
    "Lock": "锁定",
    "Unlock": "解锁",

    # Mesh Tools - 网格工具
    "Clean": "清理",
    "Merge": "合并",
    "Split": "分离",
    "Separate": "分离",
    "Join": "连接",
    "Extrude": "挤出",
    "Inset Faces": "内嵌面",
    "Loop Cut": "循环切割",
    "Knife Tool": "刀切工具",
    "Bisect": "二等分",
    "Spin": "旋转",
    "Screw": "螺旋",
    "Bridge": "桥接",
    "Grid Fill": "网格填充",
    "Beautify": "美化",
    "Triangulate": "三角化",
    "Quad": "四边形",
    "Tris": "三角形",
    "Ngons": "多边形",

    # Viewport - 视图
    "Viewport": "视图",
    "Wireframe": "线框",
    "Solid": "实体",
    "Material Preview": "材质预览",
    "Rendered": "渲染",
    "Overlays": "叠加层",
    "Gizmos": "操作器",
    "Statistics": "统计",
    "Annotations": "注释",

    # Materials - 材质
    "Material": "材质",
    "Shader": "着色器",
    "Texture": "纹理",
    "Color": "颜色",
    "Roughness": "粗糙度",
    "Metallic": "金属度",
    "Emission": "发光",
    "Alpha": "透明度",
    "Normal": "法线",
    "Bump": "凹凸",

    # Animation - 动画
    "Keyframe": "关键帧",
    "Timeline": "时间轴",
    "Frame": "帧",
    "Start": "开始",
    "End": "结束",
    "Current": "当前",
    "Play": "播放",
    "Pause": "暂停",
    "Stop": "停止",
    "Record": "录制",

    # Rendering - 渲染
    "Render": "渲染",
    "Engine": "引擎",
    "Samples": "采样",
    "Resolution": "分辨率",
    "Output": "输出",
    "Format": "格式",
    "Quality": "质量",
    "Compression": "压缩",

    # Common UI Elements - 常用界面元素
    "OK": "确定",
    "Cancel": "取消",
    "Yes": "是",
    "No": "否",
    "Save": "保存",
    "Load": "加载",
    "Import": "导入",
    "Export": "导出",
    "Open": "打开",
    "Close": "关闭",
    "New": "新建",
    "Edit": "编辑",
    "View": "查看",
    "Help": "帮助",
    "Settings": "设置",
    "Preferences": "首选项",
    "Options": "选项",
    "Properties": "属性",
    "Transform": "变换",
    "Location": "位置",
    "Rotation": "旋转",
    "Scale": "缩放",
    "Dimensions": "尺寸",
    "Origin": "原点",
    "Pivot": "轴心点",
    "Cursor": "游标",
    "Grid": "网格",
    "Snap": "吸附",
    "Proportional": "衰减",
    "Global": "全局",
    "Local": "局部",
    "Normal": "法线",
    "Gimbal": "万向节",
    "View": "视图",
    "Custom": "自定义",

    # HardOps Specific Terms - HardOps特定术语
    "Add Camera": "添加摄像机",
    "Add Lights": "添加灯光",
    "hTimer": "计时器",
    "AccuShape": "精确形状",
    "AccuShape V2": "精确形状V2",
    "Align View": "对齐视图",
    "Ever Scroll": "永续滚动",
    "Array V2": "阵列V2",
    "Mark": "标记",
    "Curve/Extract": "曲线/提取",
    "Circle": "圆形",
    "Circle (E)": "圆形 (E)",
    "Dice V2": "切块V2",
    "To_Shape": "转为形状",
    "To_Shape v1.5": "转为形状v1.5",
    "To_Shape V2": "转为形状V2",
    "EM_Macro": "编辑模式宏",
    "Mesh Tools": "网格工具",
    "MESHmachine": "网格机器",
    "Flatten/Align/Select": "平整/对齐/选择",
    "Plugin": "插件",
    "Sculpt": "雕刻",
    "Add Primitive": "添加基础体",
    "Arms": "手臂",
    "Toggle Toolbar": "切换工具栏",
    "Toggle Brush": "切换笔刷",
    "Mesh Adjustments": "网格调整",
    "Decimate": "精简",
    "Voxelize": "体素化",
    "Quadriflow": "四边流",
    "PowerSave": "省电模式",
    "Hard Ops Learning": "硬表面操作学习",
    "Lock To View": "锁定到视图",
    "Shade Solid": "实体着色",
    "Shade Wire": "线框着色",
    "Manage": "管理",
    "LookDev+": "外观开发+",
    "Voxelize Object": "体素化对象",
    "Frame Range Options": "帧范围选项",
    "Link Ops": "链接操作",
    "Keymap / Prefs": "快捷键/首选项",
    "About": "关于",

    # Status and Messages - 状态和消息
    "Finished": "完成",
    "Cancelled": "已取消",
    "Error": "错误",
    "Warning": "警告",
    "Info": "信息",
    "Success": "成功",
    "Failed": "失败",
    "Loading": "加载中",
    "Processing": "处理中",
    "Ready": "就绪",
    "Busy": "忙碌",
    "Idle": "空闲",
    "Active": "激活",
    "Inactive": "未激活",
    "Enabled": "启用",
    "Disabled": "禁用",
    "Visible": "可见",
    "Hidden": "隐藏",
    "Selected": "已选择",
    "Unselected": "未选择",
    "Locked": "已锁定",
    "Unlocked": "未锁定",

    # Object Types - 对象类型
    "Mesh": "网格",
    "Curve": "曲线",
    "Surface": "曲面",
    "Meta": "融球",
    "Text": "文本",
    "Font": "字体",
    "Armature": "骨架",
    "Lattice": "晶格",
    "Empty": "空物体",
    "Camera": "摄像机",
    "Light": "灯光",
    "Speaker": "扬声器",
    "Force Field": "力场",
    "Collection": "集合",
    "Group": "组",
    "Layer": "层",
    "Scene": "场景",
    "World": "世界",
    "Image": "图像",
    "Movie": "影片",
    "Sound": "声音",
    "Volume": "体积",
    "Grease Pencil": "蜡笔",
    "Hair": "毛发",
    "Point Cloud": "点云",
}

# 菜单标签翻译
MENU_LABELS = {
    "Hard Ops": "硬表面操作",
    "HOps": "硬表面操作",
    "Main Menu": "主菜单",
    "Settings": "设置",
    "Booleans": "布尔运算",
    "Operations": "操作",
    "Mesh Tools": "网格工具",
    "MeshTools": "网格工具",
    "Add Modifier": "添加修改器",
    "Viewport": "视图",
    "Export": "导出",
    "Import": "导入",
    "Render": "渲染",
    "Animation": "动画",
    "Sculpting": "雕刻",
    "Shading": "着色",
    "Geometry Nodes": "几何节点",
    "Compositing": "合成",
    "Texture Paint": "纹理绘制",
    "Weight Paint": "权重绘制",
    "Vertex Paint": "顶点绘制",
    "UV Editing": "UV编辑",
    "Video Editing": "视频编辑",
    "Motion Tracking": "运动跟踪",
    "Masking": "遮罩",
    "File Browser": "文件浏览器",
    "Outliner": "大纲视图",
    "Properties": "属性",
    "Timeline": "时间轴",
    "Graph Editor": "图表编辑器",
    "Dope Sheet": "摄影表",
    "NLA Editor": "NLA编辑器",
    "Text Editor": "文本编辑器",
    "Python Console": "Python控制台",
    "Info": "信息",
    "Preferences": "首选项",
    "Add-ons": "插件",
    "Themes": "主题",
    "Input": "输入",
    "Navigation": "导航",
    "Interface": "界面",
    "Lights": "灯光",
    "Materials": "材质",
    "Textures": "纹理",
    "Particles": "粒子",
    "Physics": "物理",
    "Modifiers": "修改器",
    "Constraints": "约束",
    "Object Data": "对象数据",
    "Bone": "骨骼",
    "Bone Constraints": "骨骼约束",
    "Material Properties": "材质属性",
    "Texture Properties": "纹理属性",
    "Particle Properties": "粒子属性",
    "Physics Properties": "物理属性",
    "Scene Properties": "场景属性",
    "Render Properties": "渲染属性",
    "Output Properties": "输出属性",
    "View Layer Properties": "视图层属性",
    "World Properties": "世界属性",
    "Collection Properties": "集合属性",
}

# 面板标题翻译
PANEL_LABELS = {
    "Sharp": "锐化",
    "Booleans": "布尔运算",
    "Operations": "操作",
    "Mesh Tools": "网格工具",
    "Options": "选项",
    "Settings": "设置",
    "Help": "帮助",
    "About": "关于",
    "Preferences": "首选项",
    "Keymap": "快捷键",
    "Add-ons": "插件",
    "Themes": "主题",
    "Interface": "界面",
    "Navigation": "导航",
    "Input": "输入",
    "System": "系统",
    "Save & Load": "保存和加载",
    "File Paths": "文件路径",
    "Experimental": "实验性",
    "Animation": "动画",
    "Playback": "播放",
    "Keyframing": "关键帧",
    "Auto Keyframing": "自动关键帧",
    "New F-Curve Defaults": "新F曲线默认值",
    "Transform": "变换",
    "Mesh": "网格",
    "Armature": "骨架",
    "Viewport Display": "视图显示",
    "Edit": "编辑",
    "Duplicate Data": "复制数据",
    "Undo": "撤销",
    "Grease Pencil": "蜡笔",
    "Lights": "灯光",
    "Materials": "材质",
    "Textures": "纹理",
    "Particles": "粒子",
    "Physics": "物理",
    "Modifiers": "修改器",
    "Constraints": "约束",
    "Object Data": "对象数据",
    "Bone": "骨骼",
    "Bone Constraints": "骨骼约束",
    "Material Properties": "材质属性",
    "Texture Properties": "纹理属性",
    "Particle Properties": "粒子属性",
    "Physics Properties": "物理属性",
    "Scene Properties": "场景属性",
    "Render Properties": "渲染属性",
    "Output Properties": "输出属性",
    "View Layer Properties": "视图层属性",
    "World Properties": "世界属性",
    "Collection Properties": "集合属性",
}

def translate(text):
    """
    翻译函数 - 将英文文本翻译为中文
    Translation function - Translate English text to Chinese

    Args:
        text (str): 要翻译的英文文本

    Returns:
        str: 翻译后的中文文本，如果没有找到翻译则返回原文
    """
    if not text or not isinstance(text, str):
        return text

    # 首先检查完整匹配
    if text in OPERATIONS:
        return OPERATIONS[text]
    elif text in MENU_LABELS:
        return MENU_LABELS[text]
    elif text in PANEL_LABELS:
        return PANEL_LABELS[text]

    # 如果没有找到完整匹配，返回原文
    return text

def get_translation_dict():
    """
    获取完整的翻译字典
    Get the complete translation dictionary

    Returns:
        dict: 包含所有翻译的字典
    """
    translation_dict = {}
    translation_dict.update(OPERATIONS)
    translation_dict.update(MENU_LABELS)
    translation_dict.update(PANEL_LABELS)
    return translation_dict
{"segments": 28, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.1118881106376648]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7862318754196167, 0.11538461595773697]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.75, 0.0559440478682518]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6159420609474182, 0.05594403296709061]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5797101259231567, 0.11888112127780914]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.18478260934352875, 0.11188799142837524]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.1304347813129425, 0.15734270215034485]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.1304347813129425, 0.45104900002479553]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.40942028164863586, 0.45104894042015076]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.4057971239089966, 0.6818183660507202]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6666666269302368, 0.6818187832832336]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6702898740768433, 0.45104900002479553]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8224636912345886, 0.45104900002479553]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8224637508392334, 0.7202798128128052]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7463766932487488, 0.7937067747116089]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.11594203114509583, 0.7937061190605164]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.11594204604625702, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.0863960012793541, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.08639565110206604, 0.28190186619758606]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.022295646369457245, 0.2819019854068756]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.0222960002720356, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
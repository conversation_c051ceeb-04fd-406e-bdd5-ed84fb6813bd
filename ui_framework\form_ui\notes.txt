# --- Form UI AKA Dot UI --- #

Examples

- Color Picker
    Map Scroll

    1.) Create a color element
    2.) Create a tuple 4
    3.) Pass in attr to element
    4.) Callback will be called after color was set

- Auto Resize Scroll Group
    Ever Scroll

    1.) Update the scroll Group
    2.) Get a new height value and assign to scroll scroll box
    3.) Rebuild the form

- Tips Update
    Ever Scroll

    1.) Create an element
    2.) Get the tips off the element
    3.) Set the tips update function
    4.) Function needs to return the new tips

- Button Image Group
    Ever Scroll

    1.) Call static method on Button Class to create a Group
    2.) Pass image group to Button
    3.) to change call "change_active_group_image" passing in the key

- Input Field
    Array V2 / Accu Shape / Screw / Solidify

    1.) Create a Get / Set function using the @ properties
    2.) Create the input field element passing in the attr

- Drop Down
    Ever Scroll / Accu Shape / Screw / Solidify

    1.) Create the drop down element and pass in the options
    2.) The update function needs to take in the (opt='') parram
    3.) The param is the chosen field

- Popups
    Ever Scroll

    CREATE
    1.) Create a button element
    2.) Create the popup element
    3.) Add the popup to the button

    FORM ALTER
    1.) On the popup add the params for (update_func, update_args)
    2.) In the external update method : check if the row is active with (get_row_status)
    3.) If the row is not active then use : (row_activation) to set the rows needed / not needed
    4.) If the rows where altered use trigger_rebuild and the next update cycle will rebuild once
{"segments": 44, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.26636701822280884, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2627694606781006, 0.5359712839126587]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.448014497756958, 0.2266187220811844]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6582741141319275, 0.5251795053482056]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6726621985435486, 0.028776977211236954]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8337485790252686, 0.028776999562978745]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8283528089523315, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6402881145477295, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.4568345248699188, 0.6115114688873291]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3057554066181183, 0.9964028596878052]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.09352549910545349, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.09352520108222961, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.0, 0.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
{"segments": 16, "profile": {"use_sample_straight_edges": true, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.234375]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9155846238136292, 0.234375]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9058443903923035, 0.14687508344650269]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8896104097366333, 0.234375]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8214289546012878, 0.234375]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8214285969734192, 0.33124998211860657]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7629866003990173, 0.3937501609325409]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5292213559150696, 0.39374974370002747]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5292205810546875, 0.5031251907348633]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.40259742736816406, 0.5218751430511475]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5259740352630615, 0.5406249761581421]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5292207598686218, 0.6031250357627869]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.399350643157959, 0.7500000596046448]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3961039185523987, 0.8874999284744263]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.30194807052612305, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
import bpy
from bpy.props import BoolProperty
import bpy.utils.previews
from ... utility import addon
from math import radians


class HOPS_OT_Sharpen(bpy.types.Operator):
    bl_idname = "hops.sharp_n"
    bl_label = "执行锐化"
    bl_options = {'REGISTER', 'UNDO'}
    bl_description = """左键 - 标记锐边 (软锐化)
左键 + CTRL - 添加倒角/应用布尔/标记锐边 (复杂锐化)
左键 + SHIFT - 重新计算锐边 (重新锐化)
左键 + ALT - 加权排序
左键 + CTRL + SHIFT - 移除锐边/倒角修改器 (清除锐化)
左键 + ALT + CTRL - 锐边管理器
"""

    @classmethod
    def poll(cls, context):
        return getattr(context.active_object, "type", "") == "MESH"

    def invoke(self, context, event):

        if event.ctrl and event.shift:
            bpy.ops.hops.clean_sharps()
            self.report({'INFO'}, F'锐边已取消标记')

        elif event.alt and event.ctrl:
            bpy.ops.hops.sharp_manager('INVOKE_DEFAULT',take_sharp_from={'SEAM'},apply_sharp_to={'CREASE', 'BWEIGHT', 'SEAM', 'SHARP'})
            self.report({'INFO'}, F'接缝已转换为锐边标记')

        elif event.ctrl:
            bpy.ops.hops.complex_sharpen('INVOKE_DEFAULT', is_global=True, auto_smooth_angle=addon.preference().property.auto_smooth_angle, to_bwidth=True)
            self.report({'INFO'}, F'复杂锐化完成')

        elif event.alt:
            bpy.ops.hops.mod_weighted_normal(keep_sharp=True)
            self.report({'INFO'}, F'加权法线化')
            return {'FINISHED'}

        elif event.shift:
            bpy.ops.hops.soft_sharpen('INVOKE_DEFAULT', additive_mode=False)
            self.report({'INFO'}, F'重新锐化完成')

        else:
            bpy.ops.hops.soft_sharpen('INVOKE_DEFAULT', additive_mode=True, is_global=True, auto_smooth_angle=addon.preference().property.auto_smooth_angle)
            self.report({'INFO'}, F'软锐化完成')

        return {'FINISHED'}

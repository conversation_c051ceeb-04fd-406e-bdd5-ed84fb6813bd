from bpy.types import PropertyGroup
from bpy.props import BoolProperty


class HOpsModifiers(PropertyGroup):

    array1: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    garray_random: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    garray_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    bevel1: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    bevel2: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    bevel3: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    build_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gmirror_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gmulistroke_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    ghook_falloff: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    ghook_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    glattice_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    mirrordata: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    multires_advanced: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    multires_subdivision: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    multires_shape: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    multires_generate: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    goffset_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gnoice_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gcolor_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gopacity_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gtex_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gtint_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    screw_normals: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    solidify_normals: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    solidify_materials: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    solidify_edgedata: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    solidify_thicknes: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    solidify_output: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    subsurf_advanced: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gsubsurf_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gsmooth_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gsimplify_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gthick_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    gtime_influence: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    wireframe_vgroup: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    hook_falloff: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    simpledeform_restrictions: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    warp_falloff: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    warp_textures: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    wave_start: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    wave_time: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    wave_texture: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    uvwarp_transform: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    normaledit_mix: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    normaledit_offset: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    datatransfer_toplogy: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    datatransfer_vgroups: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    datatransfer_vcolors: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    datatransfer_uv: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    transformation_map: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    transformation_to: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)

    node_outputs: BoolProperty(
        name='prop',
        description='used for panel replacement',
        default=False)
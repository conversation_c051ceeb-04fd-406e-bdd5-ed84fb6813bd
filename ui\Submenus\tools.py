import bpy
from ... icons import get_icon_id
from ... utils.addons import addon_exists
from ... utility import addon


class HOPS_MT_ObjectToolsSubmenu(bpy.types.Menu):
    bl_label = '对象工具子菜单'
    bl_idname = 'HOPS_MT_ObjectToolsSubmenu'

    def draw(self, context):
        layout = self.layout

        row = layout.column().row()

        if addon.preference().ui.expanded_menu:
            column = row.column()
        else:
            column =self.layout

        column.operator("hops.flatten_align", text="重置轴/对齐/选择", icon_value=get_icon_id("Xslap"))
        column.operator("hops.set_origin", text="设置原点", icon_value=get_icon_id("CircleSetup"))
        column.operator("hops.floor_object", text="到地面", icon_value=get_icon_id("grey"))
        column.separator()

        column.operator("hops.bool_dice_v2", text="切块 V2", icon_value=get_icon_id("Dice"))
        column.operator("hops.array_twist", text="扭转 360", icon_value=get_icon_id("ATwist360"))
        if addon.preference().property.radial_array_type == 'CLASSIC':
            column.operator("hops.radial_array", text="径向阵列", icon_value=get_icon_id("ArrayCircle"))
        else:
            column.operator("hops.radial_array_nodes", text="径向阵列 V2", icon_value=get_icon_id("ArrayCircle")).from_empty = True
        column.separator()
        column.operator("hops.taper", text="锥化/变形", icon_value=get_icon_id("Tris"))
        column.separator()

        column.operator("hops.sphere_cast", text="球形投射", icon_value=get_icon_id("SphereCast"))
        column.separator()

        column.operator("hops.edge2curve", text="曲线/提取", icon_value=get_icon_id("Curve"))
        column.operator("view3d.face_extract", text="面提取", icon_value=get_icon_id("FacePanel"))
        #column.separator()

        #column.operator("hops.st3_array", text="Array V2", icon_value=get_icon_id("GreyArrayX"))
        # column.separator()

        column.separator() #.modifier_types='BOOLEAN'
        column.operator("hops.apply_modifiers", text="智能应用", icon_value=get_icon_id("Applyall"))
        column.operator("hops.adjust_auto_smooth", text="自动平滑", icon_value=get_icon_id("Diagonal"))

        if addon.preference().ui.expanded_menu:
            column = row.column()
        else:
            column.separator()

        if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
            column.menu("HOPS_MT_MaterialListMenu", text="材质列表", icon="MATERIAL_DATA")
            if len(context.selected_objects) >= 2:
                column.operator("material.simplify", text="材质链接", icon_value=get_icon_id("Applyall"))
            #column.separator()

        column.operator_context = 'INVOKE_DEFAULT'
        column.operator("material.hops_new", text='添加空白材质', icon="PLUS")

        column.separator()

        column.operator("hops.xunwrap", text="自动展开", icon_value=get_icon_id("CUnwrap"))
        #column.separator()

        if len(context.selected_objects) == 1:
            column.operator("hops.reset_status", text="硬表面重置", icon_value=get_icon_id("StatusReset"))
            #column.separator()

        if context.active_object and context.active_object.type == 'MESH':
            column.menu("HOPS_MT_SelectViewSubmenu", text="选择选项",  icon_value=get_icon_id("ShowNgonsTris"))
            #column.separator()

        column.separator()

        column.menu("HOPS_MT_BoolScrollOperatorsSubmenu", text="修改器滚动/切换", icon_value=get_icon_id("Diagonal"))

        column.separator()

        column.menu("HOPS_MT_Export", text='导出', icon="EXPORT")
        column.separator()

        if addon_exists("MESHmachine"):
            column.separator()
            column.menu("MACHIN3_MT_mesh_machine", text="网格机器", icon_value=get_icon_id("Machine"))

        if addon_exists("Cablerator"):
            column.menu("VIEW3D_MT_cablerator", text="线缆操作", icon_value=get_icon_id("Cablerator"))

        if len(context.selected_objects) == 2:
            if addon_exists("conform_object"):
                column.menu("OBJECT_MT_conform_object", text="贴合对象", icon_value=get_icon_id("Cablerator"))
                column.separator()
            column.operator("hops.shrinkwrap2", text="收缩到", icon='GP_MULTIFRAME_EDITING')
        else:
            column.separator()

        column.operator("hops.to_gpstroke", text="转为笔画", icon="GREASEPENCIL")
        column.separator()
        if addon_exists("nSolve"):
            column.operator("nsolve.swap_mode", text="nSolve", icon_value=get_icon_id("nSolve"))

        column.operator("hops.timer", text="计时器", icon="TIME")


class HOPS_MT_MeshToolsSubmenu(bpy.types.Menu):
    bl_label = '网格工具子菜单'
    bl_idname = 'HOPS_MT_MeshToolsSubmenu'

    def draw(self, context):
        layout = self.layout
        is_boolean = len([mod for mod in bpy.context.active_object.modifiers if mod.type == 'BOOLEAN'])

        layout.operator_context = 'INVOKE_DEFAULT'
        layout.operator("hops.helper", text="修改器助手", icon="SCRIPTPLUGINS")

        layout.separator()

        layout.operator("hops.bevel_assist", text="倒角/边管理器", icon_value=get_icon_id("CSharpen"))

        layout.separator()

        layout.operator("hops.bevel_helper", text="倒角助手", icon_value=get_icon_id("ModifierHelper"))
        layout.operator("hops.sharp_manager", text="边管理器", icon_value=get_icon_id("Diagonal"))
        layout.operator("view3d.bevel_multiplier", text="倒角指数", icon_value=get_icon_id("FaceGrate"))

        layout.separator()

        if is_boolean:
            #layout.operator("hops.scroll_multi", text="Bool Multi Scroll ", icon_value=get_icon_id("Diagonal"))
            layout.operator("hops.ever_scroll_v2", text="永续滚动", icon_value=get_icon_id("StatusReset"))
            layout.operator("hops.bool_scroll_objects", text="对象滚动", icon_value=get_icon_id("StatusReset"))
            layout.separator()

        # layout.operator("hops.scroll_multi", text="Mod Scroll/Toggle", icon_value=get_icon_id("StatusReset"))
        layout.operator("hops.ever_scroll_v2", text="永续滚动", icon_value=get_icon_id("StatusReset"))

        op = layout.operator("hops.modifier_scroll", text="修改器滚动", icon_value=get_icon_id("Diagonal"))
        op.additive = True
        op.all = True

        layout.operator("hops.bool_toggle_viewport", text="切换修改器", icon_value=get_icon_id("Ngons")).all_modifiers = False

        layout.separator()

        layout.menu("HOPS_MT_Export", text='导出', icon="EXPORT")

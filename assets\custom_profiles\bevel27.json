{"segments": 20, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7572463750839233, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7536231875419617, 0.047101449221372604]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.70652174949646, 0.04710099846124649]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7065219879150391, 0.1376810073852539]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.760869562625885, 0.13768115639686584]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7554349303245544, 0.24891307950019836]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6340579986572266, 0.2499999701976776]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6195652484893799, 0.061594247817993164]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6050724387168884, 0.25]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5036231875419617, 0.2500000596046448]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5, 0.47101446986198425]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.35144928097724915, 0.6376811265945435]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.22826090455055237, 0.554347813129425]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.34057971835136414, 0.6521739363670349]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2500000298023224, 0.7536231875419617]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.25, 0.8840579390525818]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.057971060276031494, 0.8949275612831116]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.24275362491607666, 0.9057971239089966]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.239130437374115, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
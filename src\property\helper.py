from bpy.types import PropertyGroup
from bpy.props import BoolProperty


class option(PropertyGroup):
    general: <PERSON><PERSON><PERSON><PERSON><PERSON>(name="General", description="Expand or collapse panel", default = False)
    workflow: BoolProperty(name="Workflow", description="Expand or collapse panel", default = False)
    sharp: Bool<PERSON>roperty(name="Sharp", description="Expand or collapse panel", default = False)
    mesh: <PERSON><PERSON><PERSON>roper<PERSON>(name="Mesh", description="Expand or collapse panel", default = False)
    bevel: BoolProperty(name="Bevel", description="Expand or collapse panel", default = False)
    booleans: BoolProperty(name="Boolean", description="Expand or collapse panel", default = False)
    opt_ins: <PERSON><PERSON><PERSON><PERSON><PERSON>(name="Optimize It", description="Expand or collapse panel", default = False)

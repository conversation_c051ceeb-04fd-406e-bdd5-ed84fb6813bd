{"segments": 12, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.1249999850988388]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.711039125919342, 0.13750000298023224]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7175323367118835, 0.33125001192092896]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5032469034194946, 0.33125001192092896]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5064934492111206, 0.4187498986721039]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3571428060531616, 0.42812496423721313]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3571428656578064, 0.6906251311302185]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.25, 0.6874999403953552]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.24350649118423462, 0.8937501907348633]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.11688311398029327, 0.890625]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
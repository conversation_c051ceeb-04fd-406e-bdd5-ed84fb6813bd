import bpy
from bpy.props import *
from bpy.types import (Panel,
                       Operator,
                       AddonPreferences,
                       PropertyGroup,
                       )
import os
from ... icons import get_icon_id
from ... utils.addons import addon_exists

class HOPS_PT_BooleansPanel(bpy.types.Panel):
    bl_label = "布尔运算"
    # bl_category = "HardOps"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'TOOLS'
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout

        col = layout.column(align=True)

        col.operator("hops.bool_intersect", text="交集", icon="ROTATECENTER")
        col.operator("hops.bool_union", text="并集", icon="ROTATECOLLECTION")
        col.operator("hops.bool_difference", text="差集", icon="ROTACTIVE")

{"segments": 19, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.21287129819393158]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.801980197429657, 0.4207921028137207]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8663368225097656, 0.4950494170188904]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7425745129585266, 0.6336635947227478]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.45049482583999634, 0.38118812441825867]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5792080163955688, 0.5346529483795166]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3514851927757263, 0.7970302104949951]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.14851492643356323, 0.6435638666152954]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.32673266530036926, 0.8316831588745117]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.16831687092781067, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
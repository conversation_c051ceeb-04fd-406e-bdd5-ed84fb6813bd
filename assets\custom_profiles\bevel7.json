{"segments": 14, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9523807764053345, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8888886570930481, 0.7103180289268494]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7301587462425232, 0.7103180289268494]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7103175520896912, 0.5317468047142029]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6785714030265808, 0.7103180289268494]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3849206268787384, 0.7103180289268494]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.32539689540863037, 0.8650794625282288]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.23015865683555603, 0.8650797009468079]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.18253961205482483, 0.7103175520896912]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.0833333432674408, 0.7103180289268494]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.047619059681892395, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
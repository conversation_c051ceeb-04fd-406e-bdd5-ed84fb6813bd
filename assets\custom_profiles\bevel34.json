{"segments": 13, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.2499999850988388]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7629870772361755, 0.24375000596046448]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7662332653999329, 0.10312505066394806]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6428576111793518, 0.10000011324882507]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6363636255264282, 0.24375000596046448]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5000011324882507, 0.24687492847442627]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.4967532753944397, 0.5031248927116394]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.24675315618515015, 0.503125011920929]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2467532455921173, 0.753125011920929]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.30194804072380066, 0.7843748331069946]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.29870131611824036, 0.9593754410743713]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2435065060853958, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
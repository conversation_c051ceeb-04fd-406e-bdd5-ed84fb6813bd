{"segments": 11, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9983332753181458, 0.4399998188018799]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9433333277702332, 0.46000006794929504]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9416666626930237, 0.6249999403953552]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8833333849906921, 0.619999885559082]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8716666102409363, 0.4549998939037323]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7133334279060364, 0.46250009536743164]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.701666533946991, 0.60999995470047]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.46500009298324585, 0.6225001215934753]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.4583333432674408, 0.8450000286102295]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.29166656732559204, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
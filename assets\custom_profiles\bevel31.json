{"segments": 20, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.12949638068675995]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.4136689305305481]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8705032467842102, 0.41366881132125854]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8741008043289185, 0.10071941465139389]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.34532397985458374, 0.09352511167526245]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.34172725677490234, 0.2050359845161438]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8273381590843201, 0.21942445635795593]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7553957104682922, 0.25539571046829224]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.5035969614982605, 0.2517987787723541]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3669063448905945, 0.3848918378353119]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.24460431933403015, 0.28057563304901123]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.34532374143600464, 0.40287768840789795]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.25539571046829224, 0.49280595779418945]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2553960084915161, 0.5719426870346069]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9064751863479614, 0.5719429850578308]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9028775095939636, 0.6762589812278748]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2553960084915161, 0.6762590408325195]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2553960084915161, 0.7410072088241577]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
{"segments": 44, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.992806077003479, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.938830554485321, 1.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3057555854320526, 0.9964030385017395]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3093530535697937, 0.9676260948181152]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.49640390276908875, 0.6582707762718201]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.708630383014679, 0.982013463973999]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8884882926940918, 0.9856100678443909]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6079148054122925, 0.5071947574615479]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.9532346725463867, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7553959488868713, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.49280714988708496, 0.3633140027523041]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2158292829990387, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.0010000000474974513, 0.0017769355326890945]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3633090853691101, 0.5359730124473572]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.07553932815790176, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}
# -*- coding: utf-8 -*-
"""
HardOps 翻译系统
HardOps Translation System

这个模块提供了HardOps插件的翻译功能
This module provides translation functionality for the HardOps addon
"""

import bpy
from .chinese import translate as chinese_translate, get_translation_dict

# 当前语言设置
CURRENT_LANGUAGE = "chinese"

def get_language():
    """获取当前语言设置"""
    return CURRENT_LANGUAGE

def set_language(language):
    """设置当前语言"""
    global CURRENT_LANGUAGE
    CURRENT_LANGUAGE = language

def translate(text, context=None):
    """
    主翻译函数
    Main translation function
    
    Args:
        text (str): 要翻译的文本
        context (str): 翻译上下文（可选）
        
    Returns:
        str: 翻译后的文本
    """
    if not text or not isinstance(text, str):
        return text
    
    # 根据当前语言选择翻译函数
    if CURRENT_LANGUAGE == "chinese":
        return chinese_translate(text)
    
    # 如果没有找到对应语言，返回原文
    return text

def _(text):
    """
    简化的翻译函数，类似于gettext的_()函数
    Simplified translation function, similar to gettext's _() function
    """
    return translate(text)

# 为了向后兼容，提供一些常用的翻译函数
def T(text):
    """翻译函数的别名"""
    return translate(text)

def tr(text):
    """翻译函数的另一个别名"""
    return translate(text)

# 导出主要函数
__all__ = ['translate', '_', 'T', 'tr', 'get_language', 'set_language']

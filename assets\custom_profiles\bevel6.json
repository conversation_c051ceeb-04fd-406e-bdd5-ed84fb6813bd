{"segments": 17, "profile": {"use_sample_straight_edges": false, "use_sample_even_lengths": false, "points": [{"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [1.0, 0.0]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [1.0, 0.5309168696403503]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8841871023178101, 0.5223882794380188]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.8841868042945862, 0.04904051870107651]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7906460165977478, 0.04477611929178238]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.7906458377838135, 0.10447761416435242]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6302899122238159, 0.10447756201028824]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.6302893161773682, 0.21961617469787598]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.38975510001182556, 0.23240940272808075]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3942093551158905, 0.5095948576927185]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.23385298252105713, 0.5095945000648499]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.22939862310886383, 0.6545846462249756]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3853006660938263, 0.6545842289924622]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.3853006958961487, 0.8464819192886353]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.2026725709438324, 0.8464816808700562]}, {"handle_type_1": "VECTOR", "handle_type_2": "VECTOR", "location": [0.1737193763256073, 1.0]}, {"handle_type_1": "AUTO", "handle_type_2": "AUTO", "location": [0.0, 1.0]}]}}